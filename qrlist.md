# QR Code项目完整数据提取自动化方案

## 📋 **方案概述**

本方案基于Chrome MCP Server，设计了一套完整的QR Code项目数据提取自动化流程，能够系统性地提取单个QR Code项目的所有相关数据，包括主项目信息、子项目列表和翻译状态详情。

**目标系统**: GoMyHire QR码管理系统
**目标页面**: https://staging.gomyhire.com.my/s/qrCode
**核心工具**: Chrome MCP Server
**输出格式**: 结构化Markdown报告

---

## 🎯 **数据提取目标**

### 1. **QR Code主项目信息**
- **QR Code ID**: 从Edit按钮的onclick属性提取
- **QR Code名称**: 项目显示名称
- **项目状态**: 启用/禁用状态
- **创建时间**: 项目创建日期（如可获取）

### 2. **子项目完整列表**
- **sub_id**: 从Translate按钮的onclick属性提取
- **子项目名称**: 服务名称
- **子项目类型**: 自动分类（机场接送、云顶高原、票务服务、旅游服务、包车服务）
- **子项目序号**: 在列表中的位置

### 3. **翻译状态详情**
- **已有翻译语言数量**: 从#qrCodeSubTranslateTable_info提取
- **具体翻译语言列表**: 已翻译的语言名称
- **翻译完整度评估**: X/10格式
- **缺失语言识别**: 未翻译的语言列表
- **翻译质量状态**: 完整/部分/无翻译

---

## 🛠️ **Chrome MCP Server执行步骤**

### **步骤1: 定位目标QR Code项目**

使用`chrome_inject_script_chrome-mcp-stdio`注入JavaScript代码：

```javascript
// 定位目标QR Code项目
function locateTargetQrCode(targetId) {
    const editButtons = document.querySelectorAll('button[onclick*="qrCodeMainModalOpen(\'Edit\',"]');

    for (let button of editButtons) {
        const onclick = button.getAttribute('onclick');
        const match = onclick.match(/qrCodeMainModalOpen\('Edit',\s*(\d+)\)/);

        if (match && match[1] === targetId) {
            const row = button.closest('tr');
            const nameCell = row.querySelector('td:first-child');
            const statusCell = row.querySelector('td:nth-child(3)');

            return {
                qrCodeId: match[1],
                qrCodeName: nameCell ? nameCell.textContent.trim() : 'Unknown',
                status: statusCell ? statusCell.textContent.trim() : 'Unknown',
                editButton: button
            };
        }
    }
    return null;
}

// 执行定位并点击Edit按钮
const targetQrCode = locateTargetQrCode('81'); // 示例：KTMB项目
if (targetQrCode) {
    console.log('找到目标QR Code:', targetQrCode);
    targetQrCode.editButton.click();
    console.log('已打开主项目编辑窗口');
} else {
    console.error('未找到目标QR Code项目');
}
```

### **步骤2: 提取子项目列表**

等待子项目列表加载后，使用`chrome_get_web_content_chrome-mcp-stdio`获取数据：

```javascript
// 等待并提取子项目列表
function extractAllSubProjects() {
    const subProjects = [];
    const rows = document.querySelectorAll('#qrCodeSubDataTable tbody tr');

    rows.forEach((row, index) => {
        const nameCell = row.querySelector('td:first-child');
        const translateButton = row.querySelector('a[onclick*="qrCodeSubTranslate("]');

        if (nameCell && translateButton) {
            const onclick = translateButton.getAttribute('onclick');
            const match = onclick.match(/qrCodeSubTranslate\('(\d+)'\)/);

            if (match) {
                const subProjectName = nameCell.textContent.trim();
                const subId = match[1];

                // 自动分类服务类型
                const serviceType = classifyServiceType(subProjectName);

                subProjects.push({
                    序号: index + 1,
                    子项目名称: subProjectName,
                    sub_id: subId,
                    服务类型: serviceType,
                    translateButton: translateButton
                });
            }
        }
    });

    console.log('提取到的子项目列表:', subProjects);
    return subProjects;
}

// 服务类型自动分类
function classifyServiceType(serviceName) {
    const name = serviceName.toLowerCase();

    if (name.includes('airport') || name.includes('klia') ||
        (name.includes('kuala lumpur') && (name.includes('transfer') || name.includes('pickup') || name.includes('dropoff')))) {
        return '🎯 机场接送';
    } else if (name.includes('genting') && (name.includes('transfer') || name.includes('charter'))) {
        return '🎯 云顶高原';
    } else if (name.includes('ticket') || name.includes('feeding') || name.includes('fireflies') ||
               name.includes('blue tear') || name.includes('sky mirror')) {
        return '票务服务';
    } else if (name.includes('tour') || name.includes('melaka') || name.includes('selangor')) {
        return '旅游服务';
    } else if (name.includes('charter') || name.includes('hourly')) {
        return '包车服务';
    } else {
        return '其他服务';
    }
}

// 执行提取
const allSubProjects = extractAllSubProjects();
```

### **步骤3: 递归检查翻译状态**

对每个子项目逐一检查翻译状态：

```javascript
// 检查单个子项目的翻译状态
function checkSubProjectTranslation(subProject) {
    // 点击Translate按钮
    subProject.translateButton.click();

    // 等待翻译管理窗口加载
    setTimeout(() => {
        const info = document.querySelector('#qrCodeSubTranslateTable_info');
        const rows = document.querySelectorAll('#qrCodeSubTranslateTable tbody tr');

        // 解析翻译条目数量
        let totalEntries = 0;
        if (info) {
            const match = info.textContent.match(/of (\d+) entries/);
            totalEntries = match ? parseInt(match[1]) : 0;
        }

        // 提取已有语言列表
        const existingLanguages = [];
        rows.forEach(row => {
            const langCell = row.querySelector('td:first-child');
            if (langCell) {
                existingLanguages.push(langCell.textContent.trim());
            }
        });

        // 评估翻译状态
        let translationStatus;
        if (totalEntries === 0) {
            translationStatus = '❌ **无翻译 (0/10)**';
        } else if (totalEntries === 10) {
            translationStatus = '✅ **完整翻译 (10/10)**';
        } else if (totalEntries === 1 && existingLanguages.includes('English')) {
            translationStatus = '⚠️ **仅英文 (1/10)**';
        } else {
            translationStatus = `⚠️ **部分翻译 (${totalEntries}/10)**`;
        }

        console.log(`${subProject.子项目名称} (${subProject.sub_id}): ${translationStatus}`);

        // 按ESC键关闭窗口
        const escEvent = new KeyboardEvent('keydown', {
            key: 'Escape',
            code: 'Escape',
            keyCode: 27
        });
        document.dispatchEvent(escEvent);

        return {
            ...subProject,
            翻译状态: translationStatus,
            已有语言数量: totalEntries,
            已有语言列表: existingLanguages
        };
    }, 2000);
}
```

---

## 📊 **标准输出格式**

### **QR Code项目基本信息表**
```markdown
| 项目属性 | 值 |
|----------|-----|
| **QR Code ID** | 81 |
| **项目名称** | KTMB |
| **项目状态** | ✅ 启用 |
| **子项目总数** | 6个 |
| **机场接送服务** | ❌ 不包含 |
```

### **子项目详细映射表**
```markdown
| 序号 | 子项目名称 | sub_id | 翻译状态 | 服务类型 |
|------|------------|--------|----------|----------|
| 1 | **Melaka Private Tour (10 Hours)** | **468** | ❌ **无翻译 (0/10)** | 旅游服务 |
| 2 | **Kuala Selangor Private Charter (6 Hours)** | **467** | ❌ **无翻译 (0/10)** | 旅游服务 |
| 3 | **Shuttle From Genting Highland** | **466** | ✅ **完整翻译 (10/10)** | 交通服务 |
| 4 | **Shuttle To Genting Highland** | **465** | ✅ **完整翻译 (10/10)** | 交通服务 |
| 5 | **Hourly Charter 3 Hour** | **464** | ❌ **无翻译 (0/10)** | 包车服务 |
| 6 | **Hourly Charter 8 Hour** | **463** | ✅ **完整翻译 (10/10)** | 包车服务 |
```

### **翻译状态汇总表**
```markdown
| 翻译状态 | 数量 | 占比 |
|----------|------|------|
| ✅ **完整翻译 (10/10)** | 3个 | 50.0% |
| ⚠️ **部分翻译 (1-9/10)** | 0个 | 0.0% |
| ❌ **无翻译 (0/10)** | 3个 | 50.0% |
```

### **机场接送服务专项分析表**
```markdown
| 序号 | 服务名称 | sub_id | 翻译状态 | 缺失语言数 |
|------|----------|--------|----------|------------|
| 1 | **Airport transfer (Klang Valley / Kuala Lumpur)** | **459** | ⚠️ **仅英文 (1/10)** | 9种 |
| 2 | **Airport transfer (Within Kuala Lumpur Area)** | **449** | ⚠️ **仅英文 (1/10)** | 9种 |
```

---

## 🔧 **错误处理与重试机制**

### **超时处理**
```javascript
function withTimeout(operation, timeoutMs = 10000) {
    return new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
            reject(new Error('操作超时'));
        }, timeoutMs);

        operation().then(result => {
            clearTimeout(timer);
            resolve(result);
        }).catch(error => {
            clearTimeout(timer);
            reject(error);
        });
    });
}
```

### **重试机制**
```javascript
async function retryOperation(operation, maxRetries = 3, delay = 2000) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await operation();
        } catch (error) {
            if (i === maxRetries - 1) throw error;
            console.log(`操作失败，${delay}ms后进行第${i + 1}次重试...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}
```

### **进度反馈**
```javascript
function createProgressTracker(total) {
    let current = 0;
    return {
        update: (message) => {
            current++;
            const percentage = ((current / total) * 100).toFixed(1);
            console.log(`[${current}/${total}] (${percentage}%) ${message}`);
        },
        complete: () => {
            console.log(`✅ 数据提取完成 (${total}/${total})`);
        }
    };
}
```

---

## 🚀 **完整执行示例**

### **Chrome MCP Server命令序列**

1. **导航到目标页面**
```javascript
chrome_navigate_chrome-mcp-stdio({
    url: "https://staging.gomyhire.com.my/s/qrCode"
})
```

2. **注入主要提取脚本**
```javascript
chrome_inject_script_chrome-mcp-stdio({
    type: "MAIN",
    jsScript: `
        // 这里放入完整的数据提取JavaScript代码
        // 包括所有上述函数定义和执行逻辑
    `
})
```

3. **获取控制台输出**
```javascript
chrome_console_chrome-mcp-stdio({
    maxMessages: 100
})
```

4. **生成最终报告**
基于控制台输出的结构化数据，生成标准Markdown格式报告。

---

**方案创建时间**: 2025-01-27
**适用系统**: GoMyHire QR码管理系统
**技术栈**: Chrome MCP Server + JavaScript注入
**输出格式**: 结构化Markdown报告
**执行效率**: 单个项目约2-5分钟完成