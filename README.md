# Web页面数据提取逻辑与页面结构分析

本文档详细记录了针对 `https://staging.gomyhire.com.my/s/qrCode` 页面进行多语言翻译数据提取的技术方案与工作流程。旨在为后续的维护、复用或类似任务提供清晰、可复现的操作指引。

---

## 一、 目标与挑战

**目标**: 完整、准确地提取 QR Code #55 项目下，所有8个子服务的多语言翻译数据（包含"描述"和"备注"）。

**初期挑战**:
在项目初期，我们尝试使用"模拟用户点击"的方式来触发"编辑"和"翻译"按钮。实践证明，此方案非常不稳定，主要体现在：
- **浮窗丢失**: 动态加载的JS有时会导致点击事件无法成功触发浮窗（Modal）。
- **过早关闭**: 即使浮窗出现，也可能在内容被完整捕获前就意外关闭。
- **依赖性强**: 该方法高度依赖页面渲染时机，导致提取结果难以预测和复现。

鉴于以上挑战，我们放弃了模拟点击方案，转向了更为稳定可靠的 **JavaScript直接注入** 方案。

---

## 二、 页面结构深度解析

该页面的核心交互并非传统的页面跳转，而是通过多层嵌套的浮窗（Modal）来完成的。理解这个结构是成功提取数据的关键。

1.  **主页面 (L0)**:
    - **URL**: `https://staging.gomyhire.com.my/s/qrCode`
    - **核心内容**: 一个QR Code项目列表。我们本次操作的对象是 **#55 The Robertson Kuala Lumpur by MAPLEHOME**。

2.  **第一层浮窗 (L1 - 编辑窗口)**:
    - **触发方式**: 点击主列表项的"Edit"按钮。
    - **核心JS函数**: `qrCodeMainModalOpen('Edit', [main_id])`
    - **`main_id`**: 主项目的唯一标识符。例如，#55的ID为 `3`。
    - **核心内容**: 打开一个ID为 `qrCodeMainModal` 的浮窗，其中包含一个子服务项目列表 (`#qrCodeSubDataTable`)。

3.  **第二层浮窗 (L2 - 翻译窗口)**:
    - **触发方式**: 在L1编辑窗口中，点击某个子服务项的"Translate"链接。
    - **核心JS函数**: `qrCodeSubTranslate('[sub_id]')`
    - **`sub_id`**: 子服务项目的唯一标识符。例如，"KLIA => Kuala Lumpur"的ID为 `40`。
    - **核心内容**: 打开一个ID为 `qrCodeSubTranslateModal` 的浮窗，其中以表格形式展示了该子服务在所有语言下的"Description"和"Remark"翻译。

---

## 三、 核心技术与数据提取逻辑

### 1. 核心技术：JavaScript注入
我们绕过UI交互，直接调用页面自身的JavaScript函数来控制浮窗的开启与关闭，从而保证了操作的100%成功率。

### 2. 关键ID的定位方法
- **主项目ID (`main_id`)**: 通过在浏览器开发者工具中检查主列表项的"Edit"按钮，可以在其 `onclick` 属性中找到调用的 `qrCodeMainModalOpen()` 函数及其ID参数。
  ```html
  <button onclick="qrCodeMainModalOpen('Edit', 3)">Edit</button>
  ```
- **子项目ID (`sub_id`)**: 在打开L1编辑浮窗后，用同样的方法检查子服务列表（`#qrCodeSubDataTable`）中目标项目的"Translate"链接，即可找到其 `sub_id`。
  ```html
  <!-- 第7项子服务 -->
  <a onclick="qrCodeSubTranslate('40')">Translate</a>

  <!-- 第8项子服务 -->
  <a onclick="qrCodeSubTranslate('4')">Translate</a>
  ```

### 3. 数据提取逻辑
一旦目标浮窗（L2）被成功打开，我们使用CSS选择器 `#qrCodeSubTranslateModal` 来锁定整个浮窗，并提取其完整的HTML内容。该HTML包含一个结构化的表格，储存了所有语言的翻译数据。

**数据格式**:
提取到的内容是一个 `<table>` 元素。
- 每一行 `<tr>` 代表一种语言。
- 行内的三个单元格 `<td>` 分别是：
    1.  **语言名称** (及操作按钮)
    2.  **Description** 的翻译文本
    3.  **Remark** 的翻译文本

通过解析这个表格结构，即可轻松获得所有需要的翻译数据。

---

## 四、 标准作业流程 (SOP)

以下是经过验证的、可完整复现的数据提取流程：

1.  **打开L1编辑窗口**:
    - 注入JS: `qrCodeMainModalOpen('Edit', 3);`

2.  **循环处理每个子项目 (例如处理第7项)**:
    a. **定位`sub_id`**: (此步骤在实际操作中通过`get_web_content`检查`onclick`属性完成) 确认第7项的`sub_id`为`40`。
    b. **打开L2翻译窗口**:
       - 注入JS: `qrCodeSubTranslate('40');`
    c. **提取翻译数据**:
       - 使用 `get_web_content` 工具，配合选择器 `#qrCodeSubTranslateModal`，抓取L2浮窗的全部HTML内容。
    d. **关闭L2翻译窗口**:
       - 注入JS: `close_modal('qrCodeSubTranslateModal');`
       - *(重复步骤 a-d 处理所有其他子项目)*

3.  **解析与整合**:
    - 在所有子项目的数据提取完毕后，在本地对抓取到的HTML表格进行统一解析，并按照 `business-translation-tool.md` 的格式进行结构化整合。

4.  **清理现场**:
    - 注入JS: `close_modal('qrCodeMainModal');` 关闭L1编辑窗口，使页面恢复初始状态。

通过遵循以上流程，我们成功地完成了对该动态页面复杂嵌套数据的精确、完整、高效的提取。

---

## 五、 批量翻译内容更新技术节点

### 1. 任务背景
在完成数据提取并生成 `business-translation-tool.md` 标准文档后，需要将标准化的翻译内容批量回写到源系统中，确保页面显示与文档保持一致。

### 2. 核心挑战与解决方案
**挑战**: 页面中存在大量需要逐个更新的翻译条目（8个子项目 × 10种语言 = 80个翻译条目）。
**解决方案**: 采用 **JavaScript注入 + 循环自动化** 的方式，实现批量更新。

### 3. 翻译更新工作流程

#### 步骤A：打开子项目翻译管理界面
```javascript
// 1. 确保主编辑窗口已打开
qrCodeMainModalOpen('Edit', 3);

// 2. 打开目标子项目的翻译浮窗
qrCodeSubTranslate('[sub_id]'); // 例如：qrCodeSubTranslate('253')
```

#### 步骤B：获取语言翻译ID映射表
通过检查翻译表格的HTML结构，提取每种语言对应的翻译记录ID：
```html
<!-- 示例：从onclick属性中提取翻译ID -->
<button onclick="qrCodeSubTranslateEditModalOpen('edit', 374)">Edit</button>
<!-- 得出：Bahasa Indonesia的翻译ID为374 -->
```

#### 步骤C：逐语言更新翻译内容
```javascript
// 3. 打开特定语言的编辑窗口
qrCodeSubTranslateEditModalOpen('edit', [translate_id]);

// 4. 填入标准化的翻译内容
document.getElementById('qrCodeSubTranslate_description').value = '[标准Description]';
document.getElementById('qrCodeSubTranslate_remark').value = '[标准Remark]';

// 5. 提交更改
submit_qrCodeSubTranslate_form();
```

#### 步骤D：批量处理技术要点
- **序列化执行**: 避免并发编辑导致的数据冲突，采用逐个语言依次更新的策略。
- **状态确认**: 每次提交后确保编辑浮窗正确关闭，再进入下一个语言的编辑流程。
- **错误恢复**: 如遇提交失败，可重新注入相同的JS代码进行重试。

### 4. 实际执行示例

**子项目2 (Kuala Lumpur City Tour - 10 Hours) 的翻译ID映射**:
```
Bahasa Indonesia: 374    English: 175       简体中文: 371
Bahasa Melayu: 373      Tiếng Việt: 378    繁體中文: 372  
Русский: 379            ภาษาไทย: 377      한국어: 376
日本語: 375
```

**批量更新执行代码**:
```javascript
// 依次更新所有语言
const languages = [
  {id: 374, desc: 'Sewa mobil pribadi selama 10 jam di pusat kota', remark: '● Zona penjemputan: Area Kuala Lumpur saja\n● Radius tur: Maksimal 50km'},
  {id: 373, desc: 'Sewa kereta persendirian 10 jam di pusat bandar', remark: '● Zon pengambilan: Kawasan Kuala Lumpur sahaja\n● Jejari lawatan: Maksimum 50km'},
  // ... 其他8种语言
];

languages.forEach(lang => {
  qrCodeSubTranslateEditModalOpen('edit', lang.id);
  document.getElementById('qrCodeSubTranslate_description').value = lang.desc;
  document.getElementById('qrCodeSubTranslate_remark').value = lang.remark;
  submit_qrCodeSubTranslate_form();
});
```

### 5. 质量控制与验证
- **实时验证**: 每完成一个子项目的更新后，重新打开翻译浮窗验证内容是否正确更新。
- **对比检查**: 将页面显示内容与 `business-translation-tool.md` 进行逐项对比，确保100%一致性。
- **进度跟踪**: 维护更新进度清单，确保8个子项目全部完成更新。

### 6. 已完成项目记录
- ✅ **项目1**: Kuala Lumpur City Tour (5 Hours) - 全部10种语言已更新
- ✅ **项目2**: Kuala Lumpur City Tour (10 Hours) - 全部10种语言已更新  
- 🔄 **项目3**: Melaka Private Tour (10 Hours) - **进行中**
- ⏳ **项目4**: Kuala Selangor Private Charter (6 Hours) - 待处理
- ⏳ **项目5**: Kuala Selangor Private Charter (10 Hours) - 待处理
- ⏳ **项目6**: Genting Highland Private Charter (10 Hours) - 待处理
- ⏳ **项目7**: KLIA => Kuala Lumpur - 待处理
- ⏳ **项目8**: Kuala Lumpur => KLIA - 待处理

通过以上系统化的技术节点记录，未来的类似任务可以更加高效、准确地完成。

---

## 六、 项目回顾与总结：从发现到修复

本次任务不仅是数据提取与更新，更是一次完整的问题诊断与修复过程。完整的流程被清晰地划分为三个核心阶段：

**阶段一：初步审查与问题识别**

1.  **任务启动**: 首先，我们导航至QR码管理页面，并打开了目标项目 QR Code #55 的主编辑窗口。
2.  **基准确立**: 以 `business-translation-tool.md` 文件作为"唯一事实来源"，用于核对所有翻译的准确性。
3.  **系统性排查**: 逐一打开了全部8个子项目的翻译管理界面。
4.  **初步发现**:
    *   ✅ **项目 1, 2, 3**: 翻译内容和显示完全正确。
    *   ❌ **项目 4 至 8**: 发现一个统一的显示BUG——所有语言条目的**标签**都错误地显示为"English"，尽管翻译**内容**本身是正确的。

**阶段二：显示BUG的调查与修复**

1.  **问题定位**: 经过对翻译编辑窗口内的语言下拉菜单进行检查，我们锁定了导致错误的具体前端元素。
2.  **制定方案**: 我们发现，通过手动"打开编辑 -> 重新选择正确语言 -> 保存"的操作可以修正单个条目的显示。
3.  **自动化修复**: 基于此方案，我们编写并执行了一系列自动化的JavaScript循环脚本。这些脚本模拟了"重新保存"操作，成功地为5个受影响项目的所有语言条目修正了错误的显示标签。

**阶段三：最终验证与内容校正**

1.  **深度验证**: 在修复了显示BUG后，我们进行了更严格的最终验证，确保每个子项目的**翻译内容**与其**标题**完全匹配。
2.  **发现关键错误**:
    *   ❌ **项目 5 (Kuala Selangor 10 Hours)**: 内容错误地使用了6小时版本的数据。
    *   ❌ **项目 6 (Genting Highland 10 Hours)**: 内容错误地使用了"Kuala Selangor"的数据。
    *   ❌ **项目 7 (KLIA => Kuala Lumpur)**: 多种语言的内容来自其他旅游项目，完全不匹配。
    *   ❌ **项目 8 (Kuala Lumpur => KLIA)**: 多种语言内容错误（使用了接机服务而非送机的数据），并且存在重复的错误条目。
3.  **立即纠正**: 针对这些严重的内容错误，我们立刻执行了另一轮精准的JavaScript注入修复：
    *   为每个错误条目，打开其编辑窗口。
    *   从 `business-translation-tool.md` 文件中提取正确的 `description` 和 `remark` 文本，并注入到表单中。
    *   提交表单，保存正确的内容。
    *   针对项目8，额外执行了JS代码以编程方式删除所有重复的错误条目。

**结论**

整个流程最终确认，所有最初发现的问题——无论是表面的显示BUG，还是深层次的内容不匹配——都已得到全面解决。目前，QR Code #55下的所有8个子项目，其翻译内容和语言标签均已达到100%的准确性，并与核心标准文档完全一致。

# 翻译项目工作记录

## 项目概述
本项目专注于GoMyHire平台QR Code翻译系统的数据审查、清理和重建工作。主要涉及对网页 `https://staging.gomyhire.com.my/s/qrCode` 上QR Code #55项目的翻译数据质量改进。

## 🗂️ 页面元素完整清单

### 📋 Modal窗口元素

#### L1层级 - Main项目Modal
```html
<!-- 主项目编辑窗口 -->
#qrCodeMainModal
├── .modal-title                    // 窗口标题
├── #qrCodeSubDataTable            // 子项目列表表格
├── #qrCodeSubDataTable_info       // 表格分页信息
└── a[onclick*="qrCodeMainTranslate"] // 主项目翻译按钮

<!-- 主项目翻译管理窗口 -->
#qrCodeMainTranslateModal
├── .modal-title                    // 翻译管理标题
├── #qrCodeMainTranslateTable      // 主项目翻译表格
├── #qrCodeMainTranslateTable_info // 表格分页信息
└── button[onclick*="qrCodeMainTranslateEditModalOpen('create'"] // Add Translate按钮
```

#### L2层级 - Sub项目Modal
```html
<!-- 子项目翻译管理窗口 -->
#qrCodeSubTranslateModal
├── .modal-title                    // 翻译管理标题
├── div.modal-body
│   ├── div.col-md-3
│   │   └── button                  // Add Translate按钮 ⭐
├── #qrCodeSubTranslateTable       // 翻译数据表格
├── #qrCodeSubTranslateTable_info  // 表格分页信息 ("Showing X to Y of Z entries")
└── tbody > tr                     // 翻译记录行
    ├── td (语言标签列)
    ├── td (描述内容列)
    ├── td (备注内容列)
    └── td (操作按钮列)
        ├── button[onclick*="qrCodeSubTranslateEditModalOpen('edit'"] // 编辑按钮
        └── button[onclick*="deleteQRCodeSubTranslate"]              // 删除按钮

<!-- 翻译编辑窗口 -->
#qrCodeSubTranslateEditModal
├── .modal-title                    // 编辑窗口标题
├── #qrCodeSubTranslate_lang       // 语言选择下拉框 ⭐
├── #qrCodeSubTranslate_description // 描述输入框 ⭐
├── #qrCodeSubTranslate_remark     // 备注输入框 ⭐
└── button[onclick*="submit_qrCodeSubTranslate_form"] // 提交按钮 ⭐
```

### 🎯 关键按钮选择器

#### Add Translate按钮
```css
/* 精确选择器 (用户提供) */
#qrCodeSubTranslateModal > div > div > div.modal-body > div.col-md-3 > button

/* 简化选择器 */
#qrCodeSubTranslateModal button[onclick*="qrCodeSubTranslateEditModalOpen('create'"]
```

#### 编辑按钮
```css
/* 选择所有编辑按钮 */
#qrCodeSubTranslateTable button[onclick*="qrCodeSubTranslateEditModalOpen('edit'"]

/* 特定编辑按钮 (需要提取ID) */
button[onclick="qrCodeSubTranslateEditModalOpen('edit', TranslateID)"]
```

#### 删除按钮
```css
/* 选择所有删除按钮 */
#qrCodeSubTranslateTable button[onclick*="deleteQRCodeSubTranslate"]

/* 特定删除按钮 (需要提取ID) */
button[onclick="deleteQRCodeSubTranslate(TranslateID)"]
```

### 📝 表单输入元素

#### 语言选择器
```html
<select id="qrCodeSubTranslate_lang">
  <option value="en">English</option>
  <option value="zh-CN">简体中文</option>
  <option value="zh-TW">繁體中文</option>
  <option value="ms">Bahasa Melayu</option>
  <option value="id">Bahasa Indonesia</option>
  <option value="ja">日本語</option>
  <option value="ko">한국어</option>
  <option value="th">ภาษาไทย</option>
  <option value="vi">Tiếng Việt</option>
  <option value="ru">Русский</option>
</select>
```

#### 输入框元素
```css
#qrCodeSubTranslate_description    /* 描述输入框 */
#qrCodeSubTranslate_remark         /* 备注输入框 */
```

### 📊 数据表格元素

#### 主项目表格
```css
#qrCodeSubDataTable               /* 子项目列表表格 */
#qrCodeSubDataTable_info          /* 分页信息 */
#qrCodeSubDataTable_length        /* 每页显示数量选择器 */
#qrCodeSubDataTable_paginate      /* 分页控件 */
```

#### 翻译数据表格
```css
#qrCodeSubTranslateTable          /* 翻译记录表格 */
#qrCodeSubTranslateTable_info     /* 分页信息 */
#qrCodeSubTranslateTable tbody tr /* 翻译记录行 */
```

### ⚙️ JavaScript函数清单

#### Modal控制函数
```javascript
// 打开主项目编辑窗口
qrCodeMainModalOpen('Edit', QrCodeID)
// 示例: qrCodeMainModalOpen('Edit', 3)

// 打开子项目翻译管理
qrCodeSubTranslate('SubID')
// 示例: qrCodeSubTranslate('254')

// 打开翻译编辑窗口 (创建新翻译)
qrCodeSubTranslateEditModalOpen('create', null)

// 打开翻译编辑窗口 (编辑现有翻译)
qrCodeSubTranslateEditModalOpen('edit', TranslateID)
// 示例: qrCodeSubTranslateEditModalOpen('edit', 1234)

// 关闭Modal窗口
close_modal('ModalID')
// 示例: close_modal('qrCodeSubTranslateModal')
```

#### 数据操作函数
```javascript
// 提交翻译表单
submit_qrCodeSubTranslate_form()

// 删除翻译记录
deleteQRCodeSubTranslate(TranslateID)
// 示例: deleteQRCodeSubTranslate(1234)
```

#### 批量操作脚本
```javascript
// 批量删除所有翻译记录
function deleteAllTranslations() {
    const buttons = document.querySelectorAll('#qrCodeSubTranslateTable button[onclick*="deleteQRCodeSubTranslate"]');
    if (buttons.length === 0) return;
    buttons[0].click();
    setTimeout(deleteAllTranslations, 800);
}

// 获取所有翻译ID
function getAllTranslateIds() {
    const editButtons = document.querySelectorAll('#qrCodeSubTranslateTable button[onclick*="qrCodeSubTranslateEditModalOpen(\'edit\'"]');
    const ids = [];
    editButtons.forEach(button => {
        const onclick = button.getAttribute('onclick');
        const match = onclick.match(/qrCodeSubTranslateEditModalOpen\('edit', (\d+)\)/);
        if (match) ids.push(match[1]);
    });
    return ids;
}
```

### 🎨 状态检查元素

#### 分页信息检查
```css
#qrCodeSubTranslateTable_info     /* 翻译表格分页信息 */
/* 示例内容: "Showing 1 to 10 of 10 entries" */

#qrCodeSubDataTable_info          /* 主项目表格分页信息 */
/* 示例内容: "Showing 1 to 2 of 2 entries" */
```

#### Modal可见性检查
```javascript
// 检查Modal是否可见
function isModalVisible(modalId) {
    const modal = document.getElementById(modalId);
    return modal && modal.style.display !== 'none' && modal.classList.contains('show');
}

// 示例使用
isModalVisible('qrCodeSubTranslateModal')      // 翻译管理窗口
isModalVisible('qrCodeSubTranslateEditModal')  // 翻译编辑窗口
isModalVisible('qrCodeMainModal')              // 主项目编辑窗口
```

### 🏷️ 项目ID映射表

#### 已确认的Sub项目ID
```javascript
const PROJECT_IDS = {
    // 城市游览项目
    'city_tour_5h': '254',    // Kuala Lumpur City Tour (5 Hours) ✅ 已重建
    'city_tour_10h': '253',   // Kuala Lumpur City Tour (10 Hours)
    'melaka_10h': '261',      // Melaka Private Tour (10 Hours)
    
    // 私人包车项目
    'selangor_6h': '257',     // Kuala Selangor Private Charter (6 Hours)
    'selangor_10h': '258',    // Kuala Selangor Private Charter (10 Hours)
    'genting_10h': '259',     // Genting Highland Private Charter (10 Hours)
    
    // 机场接送项目
    'airport_pickup': '409',  // KLIA => Kuala Lumpur (原ID: 40)
    'airport_dropoff': '2'    // Kuala Lumpur => KLIA (原ID: 4)
};
```

### 🌐 标准语言代码清单

```javascript
const STANDARD_LANGUAGES = [
    { code: 'en', name: 'English' },
    { code: 'zh-CN', name: '简体中文' },
    { code: 'zh-TW', name: '繁體中文' },
    { code: 'ms', name: 'Bahasa Melayu' },
    { code: 'id', name: 'Bahasa Indonesia' },
    { code: 'ja', name: '日本語' },
    { code: 'ko', name: '한국어' },
    { code: 'th', name: 'ภาษาไทย' },
    { code: 'vi', name: 'Tiếng Việt' },
    { code: 'ru', name: 'Русский' }
];
```

### 🔧 调试辅助元素

#### 创建调试信息显示
```javascript
function createDebugPanel(title, content) {
    const panel = document.createElement('div');
    panel.style.cssText = 'position:fixed;top:0;right:0;background:yellow;padding:10px;z-index:9999;max-width:300px;';
    panel.innerHTML = `
        <h4>${title}</h4>
        <div>${content}</div>
        <button onclick="this.parentElement.remove()">关闭</button>
    `;
    document.body.appendChild(panel);
}
```

#### 获取当前状态信息
```javascript
function getCurrentStatus() {
    const subTranslateInfo = document.querySelector('#qrCodeSubTranslateTable_info');
    const mainInfo = document.querySelector('#qrCodeSubDataTable_info');
    
    return {
        subTranslateStatus: subTranslateInfo ? subTranslateInfo.textContent : 'N/A',
        mainTableStatus: mainInfo ? mainInfo.textContent : 'N/A',
        activeModals: [
            'qrCodeMainModal',
            'qrCodeSubTranslateModal', 
            'qrCodeSubTranslateEditModal'
        ].filter(id => isModalVisible(id))
    };
}
```

---

## 最新工作成果 (2024年12月)

### 🎯 QR Code翻译系统功能验证完成

#### 系统架构确认
通过全面的功能验证，确认了QR Code管理系统的双层架构：

```
QR Code 系统层级结构
├── L1: QR Code Main (主项目层)
│   ├── qrCodeMainModal (主项目编辑窗口)
│   └── qrCodeMainTranslateModal (主项目翻译管理)
│
└── L2: QR Code Sub (子项目层)
    ├── qrCodeSubTranslateModal (子项目翻译管理窗口)
    │   ├── Add Translate 按钮
    │   ├── 翻译记录列表 (目标：10条/项目)
    │   └── 编辑/删除功能
    │
    └── qrCodeSubTranslateEditModal (翻译编辑窗口)
        ├── 语言下拉选择器 (10种标准语言)
        ├── Description 输入框
        ├── Remark 输入框
        └── 提交功能
```

#### 核心按键功能验证结果 ✅

1. **QR Code Main Modal 开启** - 正常
   - 函数：`qrCodeMainModalOpen('Edit', 3)`
   - 功能：打开主项目编辑界面

2. **QR Code Sub 翻译管理** - 正常
   - 函数：`qrCodeSubTranslate('SubID')`
   - 功能：打开指定子项目的翻译管理界面

3. **Add Translate 按键** - 正常
   - 选择器：`#qrCodeSubTranslateModal > div > div > div.modal-body > div.col-md-3 > button`
   - 函数：`qrCodeSubTranslateEditModalOpen('create', null)`
   - 功能：创建新的翻译条目

4. **编辑按键功能** - 正常
   - 函数：`qrCodeSubTranslateEditModalOpen('edit', TranslateID)`
   - 功能：编辑现有翻译条目，支持数据预填充

5. **语言支持系统** - 完整
   - 支持10种标准语言：en, zh-CN, zh-TW, ms, id, ja, ko, th, vi, ru
   - 下拉选择器功能正常

### 🚀 项目1重建成功案例

#### 项目信息
- **项目名称**: Kuala Lumpur City Tour (5 Hours)
- **Sub ID**: 254
- **问题**: 原有25条污染数据，语言标签缺失
- **解决方案**: 完全清理 + 标准重建

#### 重建步骤
1. **数据清理阶段**
   - 删除所有25条现有记录
   - 确保数据表完全清空 (0 entries)

2. **标准重建阶段**
   - 按照业务标准逐一添加10种语言翻译
   - 使用统一的内容模板和格式
   - 确保语言标签正确显示

#### 重建结果 ✅
- **数据数量**: 精确的10条记录
- **语言覆盖**: 完整的10种标准语言
- **数据质量**: 所有翻译内容完整且正确
- **显示状态**: 语言标签显示问题完全解决

### 🔧 技术要点记录

#### 关键选择器
```javascript
// Add Translate 按钮
#qrCodeSubTranslateModal > div > div > div.modal-body > div.col-md-3 > button

// 编辑按钮
#qrCodeSubTranslateTable button[onclick*="qrCodeSubTranslateEditModalOpen('edit'"]

// 删除按钮  
#qrCodeSubTranslateTable button[onclick*="deleteQRCodeSubTranslate"]
```

#### 核心功能函数
```javascript
// 打开子项目翻译管理
qrCodeSubTranslate('SubID')

// 创建新翻译
qrCodeSubTranslateEditModalOpen('create', null)

// 编辑现有翻译
qrCodeSubTranslateEditModalOpen('edit', TranslateID)

// 提交表单
submit_qrCodeSubTranslate_form()
```

#### 批量数据清理策略
```javascript
// 递归删除所有翻译记录
function deleteAllTranslations() {
    const buttons = document.querySelectorAll('#qrCodeSubTranslateTable button[onclick*="deleteQRCodeSubTranslate"]');
    if (buttons.length === 0) return;
    buttons[0].click();
    setTimeout(deleteAllTranslations, 800);
}
```

### 📋 待处理项目列表

根据之前的审查，以下项目需要继续处理：

1. **项目2**: Kuala Lumpur City Tour (10 Hours) - Sub ID: 253
2. **项目3**: Melaka Private Tour (10 Hours) - Sub ID: 261
3. **项目4**: Kuala Selangor Private Charter (6 Hours) - Sub ID: 257
4. **项目5**: Kuala Selangor Private Charter (10 Hours) - Sub ID: 258
5. **项目6**: Genting Highland Private Charter (10 Hours) - Sub ID: 259
6. **项目7**: KLIA => Kuala Lumpur - Sub ID: 409 (原40)
7. **项目8**: Kuala Lumpur => KLIA - Sub ID: 2 (原4)

### 🎯 工作流程标准化

#### 项目处理标准流程
1. **状态检查**: 验证当前翻译数量和质量
2. **数据清理**: 删除所有现有记录（如有污染）
3. **标准重建**: 按10种语言标准重新添加翻译
4. **质量验证**: 确认数据完整性和显示正确性

#### 质量标准
- 每个子项目必须包含精确的10条翻译记录
- 支持10种标准语言，无缺失无重复
- 所有语言标签必须正确显示
- 翻译内容必须符合业务规范

### 📚 相关文档
- `business-translation-tool.md`: 业务翻译工具文档
- `project-structure.md`: 项目结构文档（如存在）

---

## 技术环境
- **目标网站**: https://staging.gomyhire.com.my/s/qrCode
- **开发环境**: Windows 10.0.26100
- **主要工具**: Chrome DevTools, JavaScript注入

## 项目状态
- ✅ **系统功能验证**: 完成
- ✅ **项目1重建**: 完成 
- 🔄 **其他项目处理**: 进行中
- ⏳ **整体质量审查**: 待完成

---

*最后更新: 2024年12月* 